const admin = require('firebase-admin');
const Notification = require('../models/notification.model');
const NotificationRecipient = require('../models/notification-recipient.model');
const { logger } = require('../utils/logger.utils');
const constantUtils = require('../utils/constants.utils');

exports.sendNotification = async (fcmToken, notificationData) => {
  try {
    if (!fcmToken) {
      throw new Error(constantUtils.FCM_TOKEN_REQUIRED);
    }
    if (!notificationData?.title || !notificationData?.body) {
      throw new Error(constantUtils.NOTIFICATION_TITLE_AND_BODY_REQUIRED);
    }

    const payload = {
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      token: fcmToken,
    };

    const response = await admin.messaging().send(payload);
    if (response.failureCount > 0) {
      throw new Error(constantUtils.NOTIFICATION_SENT_FAILED);
    }

    logger.info(constantUtils.NOTIFICATION_SENT, { response, fcmToken });
    return response;
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED, { error, fcmToken });
  }
};

/**
 * Send notifications to multiple FCM tokens
 *
 * @param {Array} fcmTokens - Array of FCM tokens
 * @param {Object} notificationData - Notification data with title and body
 * @returns {Promise<Array>} Array of results for each token
 */
exports.sendBatchNotifications = async (fcmTokens, notificationData) => {
  try {
    if (!fcmTokens || fcmTokens.length === 0) {
      throw new Error(constantUtils.FCM_TOKEN_REQUIRED);
    }
    if (!notificationData?.title || !notificationData?.body) {
      throw new Error(constantUtils.NOTIFICATION_TITLE_AND_BODY_REQUIRED);
    }

    const results = [];

    for (const fcmToken of fcmTokens) {
      try {
        const result = await this.sendNotification(fcmToken, notificationData);
        results.push({
          fcmToken,
          success: true,
          result,
        });
      } catch (error) {
        logger.error(constantUtils.NOTIFICATION_SENT_FAILED, { error, fcmToken });
        results.push({
          fcmToken,
          success: false,
          error: error.message,
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    logger.info(`Batch notification results: ${successCount}/${fcmTokens.length} successful`, {
      totalTokens: fcmTokens.length,
      successCount,
      failureCount: fcmTokens.length - successCount,
    });

    return results;
  } catch (error) {
    logger.error('Error in batch notification sending', { error: error.message });
    throw error;
  }
};

exports.getNotificationByFilter = async filter => {
  return NotificationRecipient.find({ recipient: filter.user, deletedAt: null })
    .sort({ createdAt: -1 })
    .populate({
      path: 'notification',
      match: { account: filter.account, deletedAt: null },
      populate: [
        { path: 'sender', select: 'firstName lastName email' },
        { path: 'account', select: 'name' },
      ],
      select: { __v: 0, updatedAt: 0, createdAt: 0, updatedBy: 0, createdBy: 0, deletedAt: 0 },
    })
    .select({ updatedAt: 0, createdAt: 0, __v: 0, deletedAt: 0, recipient: 0 })
    .lean();
};

/**
 * Create notification record in database
 *
 * @param {Object} notificationData - Notification data
 * @returns {Promise<Object>} Created notification
 */
exports.createNotification = async notificationData => {
  try {
    const notification = await Notification.create(notificationData);
    logger.info('Notification created successfully', { notificationId: notification._id });
    return notification;
  } catch (error) {
    logger.error('Error creating notification', { error: error.message });
    throw error;
  }
};
