const admin = require('firebase-admin');
const Notification = require('../models/notification.model');
const { logger } = require('../utils/logger.utils');
const constantUtils = require('../utils/constants.utils');

exports.sendNotification = async (fcmToken, notificationData) => {
  try {
    if (!fcmToken) {
      throw new Error(constantUtils.FCM_TOKEN_REQUIRED);
    }
    if (!notificationData?.title || !notificationData?.body) {
      throw new Error(constantUtils.NOTIFICATION_TITLE_AND_BODY_REQUIRED);
    }

    const payload = {
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      token: fcmToken,
    };

    const response = await admin.messaging().send(payload);
    if (response.failureCount > 0) {
      throw new Error(constantUtils.NOTIFICATION_SENT_FAILED);
    }

    logger.info(constantUtils.NOTIFICATION_SENT, { response, fcmToken });
    return response;
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED, { error, fcmToken });
  }
};

exports.getNotificationByFilter = async filter => {
  return await Notification.find(filter);
};
