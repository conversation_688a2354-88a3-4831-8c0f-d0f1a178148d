const admin = require('firebase-admin');
const Notification = require('../models/notification.model');
const NotificationRecipient = require('../models/notification-recipient.model');
const { logger } = require('../utils/logger.utils');
const constantUtils = require('../utils/constants.utils');

/**
 * Send bulk notifications - handles database entries, recipient entries, and push notifications for multiple recipients
 *
 * @param {Object} params - Notification parameters
 * @param {string} params.title - Notification title
 * @param {string} params.body - Notification body
 * @param {string} params.module - Notification module (e.g., 'certificate_approval', 'shift', etc.)
 * @param {string} params.priority - Notification priority ('low', 'medium', 'high')
 * @param {string} params.actionUrl - Deep link URL (optional)
 * @param {string} params.accountId - Account ID
 * @param {string} params.senderId - Sender user ID
 * @param {Array} params.recipients - Array of recipient user objects with _id, fcmTokens
 * @returns {Promise<Object>} Result object with success status and details
 */
exports.sendBulkNotifications = async params => {
  try {
    const {
      title,
      body,
      module,
      priority = 'medium',
      actionUrl = null,
      accountId,
      senderId,
      recipients,
    } = params;

    // Validate required parameters
    if (
      !title ||
      !body ||
      !module ||
      !accountId ||
      !senderId ||
      !recipients ||
      !Array.isArray(recipients)
    ) {
      throw new Error('Missing required parameters for bulk notification');
    }

    if (recipients.length === 0) {
      logger.warn('No recipients provided for bulk notification', { accountId });
      return {
        success: true,
        message: 'No recipients found for notification',
        notificationId: null,
        recipientCount: 0,
        pushNotificationResults: [],
      };
    }

    // Create single notification record
    const notification = await Notification.create({
      title,
      body,
      module,
      priority,
      actionUrl,
      account: accountId,
      sender: senderId,
      createdBy: senderId,
    });

    console.log('testing the recipients', recipients);
    // Create notification recipient records for all recipients
    const notificationRecipients = recipients.map(recipient => ({
      notification: notification._id,
      recipient: recipient._id,
    }));

    await NotificationRecipient.insertMany(notificationRecipients);

    // Send push notifications to all recipients
    const pushResults = [];

    for (const recipient of recipients) {
      if (recipient.fcmTokens && recipient.fcmTokens.length > 0) {
        for (const fcmToken of recipient.fcmTokens) {
          try {
            const pushResult = await this.sendNotification(fcmToken, { title, body });
            pushResults.push({
              recipientId: recipient._id,
              fcmToken,
              success: true,
              result: pushResult,
            });
          } catch (pushError) {
            logger.error('Failed to send push notification', {
              error: pushError.message,
              recipientId: recipient._id,
              fcmToken,
            });
            pushResults.push({
              recipientId: recipient._id,
              fcmToken,
              success: false,
              error: pushError.message,
            });
          }
        }
      } else {
        logger.warn('Recipient has no FCM tokens', { recipientId: recipient._id });
        pushResults.push({
          recipientId: recipient._id,
          fcmToken: null,
          success: false,
          error: 'No FCM tokens available',
        });
      }
    }

    const successfulPushes = pushResults.filter(result => result.success).length;

    logger.info('Bulk notifications sent successfully', {
      notificationId: notification._id,
      recipientCount: recipients.length,
      successfulPushes,
      totalPushAttempts: pushResults.length,
    });

    return {
      success: true,
      message: 'Bulk notifications sent successfully',
      notificationId: notification._id,
      recipientCount: recipients.length,
      pushNotificationResults: pushResults,
    };
  } catch (error) {
    logger.error('Error sending bulk notifications', {
      error: error.message,
      params,
    });
    throw error;
  }
};

exports.sendNotification = async (fcmToken, notificationData) => {
  try {
    if (!fcmToken) {
      throw new Error(constantUtils.FCM_TOKEN_REQUIRED);
    }
    if (!notificationData?.title || !notificationData?.body) {
      throw new Error(constantUtils.NOTIFICATION_TITLE_AND_BODY_REQUIRED);
    }

    const payload = {
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      token: fcmToken,
    };

    const response = await admin.messaging().send(payload);
    if (response.failureCount > 0) {
      throw new Error(constantUtils.NOTIFICATION_SENT_FAILED);
    }

    logger.info(constantUtils.NOTIFICATION_SENT, { response, fcmToken });
    return response;
  } catch (error) {
    logger.error(constantUtils.NOTIFICATION_SENT_FAILED, { error, fcmToken });
  }
};

exports.getNotificationByFilter = async filter => {
  return NotificationRecipient.find({ recipient: filter.user, deletedAt: null })
    .sort({ createdAt: -1 })
    .populate({
      path: 'notification',
      match: { account: filter.account, deletedAt: null },
      populate: [
        { path: 'sender', select: 'firstName lastName email' },
        { path: 'account', select: 'name' },
      ],
      select: { __v: 0, updatedAt: 0, createdAt: 0, updatedBy: 0, createdBy: 0, deletedAt: 0 },
    })
    .select({ updatedAt: 0, createdAt: 0, __v: 0, deletedAt: 0, recipient: 0 })
    .lean();
};
