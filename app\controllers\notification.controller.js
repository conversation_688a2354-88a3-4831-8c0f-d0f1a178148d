const notificationService = require('../services/notification.service');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get all notification
 *
 * @param {Object} req
 * @param {Object} res
 */
exports.getAllNotification = async (req, res) => {
  try {
    const filter = {
      account: req.userData.account,
      deletedAt: null,
      user: req.userData._id,
    };

    const page = req.query.page;
    const perPage = req.query.perPage;

    const response = await notificationService.getNotificationByFilter(filter, page, perPage);
    res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.NOTIFICATION_FETCHED, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update notification read status
 *
 * @param {Object} req
 * @param {Object} res
 */
exports.updateNotificationReadStatus = async (req, res) => {
  try {
    const { notificationRecipientId } = req.params;
    const { isRead } = req.body;

    await notificationService.updateNotificationReadStatus(notificationRecipientId, isRead);

    res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.NOTIFICATION_READ_STATUS_UPDATED));
  } catch (error) {
    res
      .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
      .json(errorResponse(constantUtils.NOTIFICATION_NOT_FOUND));
  }
};
