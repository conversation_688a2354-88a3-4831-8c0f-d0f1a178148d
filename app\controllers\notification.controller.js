const notificationService = require('../services/notification.service');

const { successResponse, errorResponse } = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const HTTP_STATUS = require('../utils/status-codes');

exports.getAllNotification = async (req, res) => {
  try {
    const filter = {
      account: req.userData.account,
      deletedAt: null,
    };

    const response = await notificationService.getNotificationByFilter(filter);
    res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.NOTIFICATION_FETCHED, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
