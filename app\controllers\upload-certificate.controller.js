const dateFormat = require('dateformat');
// Services
const uploadCertificateService = require('../services/upload-certificate.service');
const userService = require('../services/user.service');
const certificateTypeService = require('../services/certificate-type.service');
const accountService = require('../services/account.service');
const certificateNotificationService = require('../services/certificate-notification.service');
// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const mailerUtils = require('../utils/mailer.utils');
const { toObjectId } = require('../utils/common.utils');
const {
  APPROVE_IMAGE_SUBJECT,
  APPROVE_EMAIL_MESSAGE,
  APPROVE_IMAGE_TYPE,
  REJECT_IMAGE_SUBJECT,
  REJECT_EMAIL_MESSAGE,
  REJECT_IMAGE_TYPE,
  REJECT_REASON,
} = constantUtils;
const commonUtils = require('../utils/common.utils');
const { validateSearch } = require('../utils/common-function.utils');

const mongoose = require('mongoose');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create Upload Certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.createUploadCertificate = async (req, res) => {
  try {
    let { user, files } = req.body;
    const urls = [];
    for (const file of files) {
      let validityDate;
      if (typeof file.validityDate === 'boolean') {
        validityDate = file.validityDate;
      } else if (typeof file.validityDate === 'string' || file.validityDate instanceof Date) {
        validityDate = new Date(file.validityDate);
      }
      urls.push({
        certificate: file.certificate,
        function: file.function,
        link: file.link,
        fileName: file.fileName,
        size: file.size,
        fromDate: file.fromDate,
        toDate: file.toDate,
        name: file.name,
        validityDate,
        deletedAt: file.deletedAt,
      });
    }
    const alreadyExist = await uploadCertificateService.getUploadCertificateById(req.body.user);
    if (alreadyExist) {
      alreadyExist.files = alreadyExist.files.concat(urls);
      await alreadyExist.save();
      return res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS, alreadyExist));
    }
    files = urls;
    const reqData = { user, files };
    const response = await uploadCertificateService.createUploadCertificate(reqData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get User Certificates
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getUserCertificates = async (req, res) => {
  try {
    const filter = {
      account: req.userData.account,
      deletedAt: null,
    };

    const certificateList = await certificateTypeService.getUserCertificatesList(filter);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_CERTIFICATE_LIST, certificateList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
/**
 * Update Upload Certificates
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateFiles = async (req, res) => {
  try {
    const { _id } = req.userData;
    const alreadyExist = await uploadCertificateService.getUploadCertificateById(_id);

    if (alreadyExist) {
      await uploadCertificateService.updateDetail(alreadyExist._id, req.body.files);
      const response = await uploadCertificateService.getUploadCertificateById(_id);
      return res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_UPDATE, response));
    } else {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
    }
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.sendEmail = async (req, res) => {
  try {
    let email = req.body.email;
    let user = await userService.getByEmail(email?.toLowerCase());
    const fileId = req.params.fileId;
    const certificateName = await certificateTypeService.getCertificateTypeById(
      req.body.certificateId,
      fileId
    );
    if (!certificateName) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
    }

    const admin = await accountService.findAccountById(user.account);
    const template = process.env.SENDGRID_APPROVED_MAIL_TEMPLATE;

    let templateData = {
      name: user?.callingName ? user.callingName : user.firstName,
      certificateName: certificateName ? certificateName.name : null,
      companyName: admin.name,
      date: new Date().toISOString().slice(0, 10),
      adminEmail: admin.email,
      urlLink: req.body.link,
      logo: global.constant.APP_LOGO,
      image: req.body.status
        ? global.constant.CERTIFICATE_APPROVE_IMAGE
        : global.constant.CERTIFICATE_REJECT_IMAGE,
      subject: req.body.status === 'approved' ? APPROVE_IMAGE_SUBJECT : REJECT_IMAGE_SUBJECT,
      message: req.body.status === 'approved' ? APPROVE_EMAIL_MESSAGE : REJECT_EMAIL_MESSAGE,
      type: req.body.status === 'approved' ? APPROVE_IMAGE_TYPE : REJECT_IMAGE_TYPE,
      ...(req.body.status !== 'approved' && {
        reason: req.body.reason,
        rejectReason: REJECT_REASON,
      }),
    };

    let update = {
      status: req.body.status,
      fileId: toObjectId(fileId),
    };

    await uploadCertificateService.updateApproval(fileId, user._id, update);
    await mailerUtils.sendMailer(email, template, { templateData });

    return res.status(200).json(responseUtils.successResponse(constantUtils.EMAIL_APPROVAL));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
/**
 * Get Approval Certificate List
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.certificateList = async (req, res) => {
  try {
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }
    let filter = {
      account: req.userData.account,
      status: req.query.status ?? 'pending',
      deletedAt: null,
    };

    if (req.query?.search && mongoose.Types.ObjectId.isValid(req.query?.search)) {
      filter.user = toObjectId(req.query?.search);
    }

    let page = req.query.page ? parseInt(req.query.page) : 0;
    let perPage = req.query.perPage ? parseInt(req.query.perPage) : 10;
    let sortList = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    const certificates = await uploadCertificateService.getUploadedCertificates(
      filter,
      page,
      perPage,
      sortList
    );

    // add all records count
    let finalResponse = {
      certificateData: certificates,
      currentPage: Number(page),
    };
    finalResponse = await commonUtils.getCountFromQuery(
      'upload-certificate',
      filter,
      finalResponse
    );

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_CERTIFICATE_LIST, finalResponse));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create User Certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.createUserCertificate = async (req, res) => {
  try {
    let { files, user } = req.body;

    const userId = user || req.userData._id; // if user key is present
    const isAdmin = Boolean(user); // if user is admin

    Object.keys(files).forEach(key => {
      files[key].user = userId;
      files[key].account = req.userData.account;
      files[key].createdBy = userId;
    });

    // replace certificate name
    for (let key in files) {
      files[key] = await this.replaceCertificateName(files[key], isAdmin);
    }

    files.forEach(file => {
      if (req.userData.isMobile == false) {
        file.size = (file.size / global.constant.BINARY_CONVERSION_FACTOR).toFixed(2); // bytes to kb conversion
      }

      if (file.endDate && new Date(file.endDate) < new Date()) {
        file.status = 'expired';
      }
    });

    const response = await uploadCertificateService.uploadCertificateInsertMany(files);

    // Send web-to-mobile notifications for non-internal certificates uploaded by admin
    if (isAdmin && req.userData.isMobile === false) {
      try {
        // Check if any uploaded certificates are not marked as internal
        const nonInternalCertificates = files.filter(file => !file.internal);

        if (nonInternalCertificates.length > 0) {
          // Get user details for notification
          const targetUser = await userService.getUserById(userId);
          const certificateNames = nonInternalCertificates.map(file => file.name).filter(Boolean);

          const notificationData = {
            accountId: req.userData.account,
            senderId: req.userData._id,
            certificateName:
              certificateNames.length > 0 ? certificateNames.join(', ') : 'Certificate',
            userName: targetUser?.callingName || targetUser?.firstName || 'User',
          };

          certificateNotificationService
            .sendWebToMobileNotification(notificationData)
            .then(result => {
              console.log('Web-to-mobile certificate notification sent:', result);
            })
            .catch(error => {
              console.error('Failed to send web-to-mobile certificate notification:', error);
            });
        }
      } catch (notificationError) {
        // Log error but don't fail the main operation
        console.error('Error in certificate notification process:', notificationError);
      }
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update User Certificate
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateUserCertificate = async (req, res) => {
  try {
    const { id } = req.params;
    let { status } = req.body;
    const reqData = req.body;
    const exist = await uploadCertificateService.getSingleUploadCertificateByFilter({
      _id: id,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
    }
    const filterData = {
      user: exist.user,
      certificateType: exist.certificateType,
      isActive: true,
      deletedAt: null,
    };
    if (status === 'approved') {
      await uploadCertificateService.updateCertificateByFilter(filterData, {
        isActive: false,
      });
      reqData.isActive = true;
    }
    const response = await uploadCertificateService.updateDetail(id, reqData);
    if (response && status && ['approved', 'rejected'].includes(status)) {
      await this.sendStatusEmail(response, status, req);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_UPDATE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Send email to user on approved or rejected
 *
 * @param {*} response
 * @param {*} status
 */
exports.sendStatusEmail = async (response, status, req) => {
  try {
    const template = process.env.SENDGRID_APPROVED_MAIL_TEMPLATE;
    const user = await userService.getUserById(response.user);
    const getCertificateType = await certificateTypeService.getCertificateTypeById(
      response.certificateType
    );

    let templateData = {
      userName: `${user[0]?.callingName ? user[0].callingName : user[0].firstName} ${
        user[0].lastName
      }`,
      certificateName: getCertificateType ? getCertificateType.name : null,
      currentYear: new Date().getFullYear(),
      actionDate: new Date().toISOString().slice(0, 10),
      urlLink: response.link,
      bestRegards: process.env.BEST_REGARDS,
      issuerName:
        (req.userData?.callingName ? req.userData.callingName : req.userData.firstName) +
        ' ' +
        req.userData.lastName,
      supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
      logo: global.constant.APP_LOGO,
      image:
        status === 'approved'
          ? global.constant.CERTIFICATE_APPROVE_IMAGE
          : global.constant.CERTIFICATE_REJECT_IMAGE,
      subject: status === 'approved' ? APPROVE_IMAGE_SUBJECT : REJECT_IMAGE_SUBJECT,
      message: status === 'approved' ? APPROVE_EMAIL_MESSAGE : REJECT_EMAIL_MESSAGE,
      certificateType: status === 'approved' ? APPROVE_IMAGE_TYPE : REJECT_IMAGE_TYPE,
      ...(status !== 'approved' && {
        reason: response.reason,
      }),
    };
    await mailerUtils.sendMailer(user[0].email, template, templateData);
  } catch (error) {
    console.log(error);
  }
};

/**
 * Update User Certificate In Bulk
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateUserCertificatesInBulk = async (req, res) => {
  try {
    let { files } = req.body;

    for (let key in files) {
      if ('id' in files[key]) {
        let { id } = files[key];
        delete files[key].id;
        let exist = await uploadCertificateService.getSingleUploadCertificateByFilter({
          _id: id,
          account: req.userData.account,
          deletedAt: null,
        });
        if (!exist) {
          return res
            .status(400)
            .json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
        }
        await this.userCertificateOperation(files[key], exist, req);
      } else {
        const processResponse = await this.processForNewCertificateUpload(files[key], req, res);
        if (processResponse?.statusCode) {
          return processResponse;
        }
      }
    }

    // Send mobile-to-web notifications for new certificate uploads from mobile
    if (req.userData.isMobile === true) {
      try {
        // Check if any new certificates were uploaded (not just updated)
        const newCertificates = [];
        for (let key in files) {
          if (!('id' in files[key])) {
            newCertificates.push(files[key]);
          }
        }

        if (newCertificates.length > 0) {
          // Get user details for notification
          const uploader = await userService.getUserById(req.userData._id);
          const certificateNames = newCertificates.map(file => file.name).filter(Boolean);

          const notificationData = {
            accountId: req.userData.account,
            senderId: req.userData._id,
            certificateName:
              certificateNames.length > 0 ? certificateNames.join(', ') : 'Certificate',
            userName: uploader?.callingName || uploader?.firstName || 'User',
          };

          // Send notification asynchronously to avoid blocking the response
          certificateNotificationService
            .sendMobileToWebNotification(notificationData)
            .then(result => {
              console.log('Mobile-to-web certificate notification sent:', result);
            })
            .catch(error => {
              console.error('Failed to send mobile-to-web certificate notification:', error);
            });
        }
      } catch (notificationError) {
        // Log error but don't fail the main operation
        console.error('Error in mobile certificate notification process:', notificationError);
      }
    }

    return res.status(200).json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_UPDATE));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * User Certificate Operation (Update or Delete)
 *
 * @param {*} fileData
 * @param {*} existData
 */
exports.userCertificateOperation = async (fileData, existData, req) => {
  // delete certitificate
  if ('isDelete' in fileData && fileData.isDelete === true) {
    await uploadCertificateService.deleteCertificate(existData._id, {
      deletedBy: req.userData._id,
      deletedAt: new Date(),
    });
  } else {
    fileData = await this.changeFileNameOnEndDate(fileData, existData); // change file name if end date is changed
    await uploadCertificateService.updateDetail(existData._id, fileData); // update certificate
  }
};

/**
 * Process For New Certificate Upload
 *
 * @param {*} fileData
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.processForNewCertificateUpload = async (fileData, req, res) => {
  const { account, _id } = req.userData;
  const userId = req.body.user || _id;
  let commonFilter = {
    user: userId,
    certificateType: fileData.certificateType,
    account,
    deletedAt: null,
  };
  let exist = await uploadCertificateService.getSingleUploadCertificateByFilter(commonFilter);

  if (exist?.status === 'pending') {
    return res
      .status(400)
      .json(responseUtils.errorResponse(constantUtils.CERTIFICATE_TYPE_DOCUMENT_EXIST));
  }

  if (fileData?.renew) {
    fileData = await this.prepareRenewCertificate({ ...commonFilter, user: userId }, fileData);
  }
  fileData.isActive = false;
  fileData.user = userId;
  fileData.account = account;
  fileData.createdBy = userId;
  fileData = await this.replaceCertificateName(fileData); // change file name
  const newCertificate = await uploadCertificateService.createUploadCertificate(fileData);

  // Return the created certificate data for notification purposes
  return { newCertificate, fileData };
};

/**
 * Prepare File Data For Renew Certificate
 *
 * @param {*} filter
 * @param {*} fileData
 * @returns
 */
exports.prepareRenewCertificate = async (filter, fileData) => {
  const getCertificateData = await uploadCertificateService.getUserUploadCertificate(filter);
  fileData.version = getCertificateData.length + 1;
  return fileData;
};

/**
 * Replace certificate name
 *
 * @param {*} fileDetails
 * @returns
 */
exports.replaceCertificateName = async (fileDetails, isAdmin = false) => {
  try {
    if ('fileName' in fileDetails && 'user' in fileDetails) {
      const userDetails = await userService.getUserById(fileDetails.user);
      let certificateType = await certificateTypeService.getCertificateTypeById(
        fileDetails.certificateType
      );
      certificateType.name = certificateType.name.replace(/\s/g, '_');
      let expiryDate =
        fileDetails.endDate != null ? `_${dateFormat(fileDetails.endDate, 'dd-mm-yyyy')}` : '';
      let uploadedFile = fileDetails.fileName.split('.');

      let extension = uploadedFile[uploadedFile.length - 1];
      fileDetails.fileName = `${
        userDetails[0]?.callingName ? userDetails[0].callingName : userDetails[0].firstName
      }_${userDetails[0].lastName}_${certificateType.name}${expiryDate}.${extension}`;

      // check if file uploaded by admin then mark it as approved
      if (isAdmin) {
        // mark previous certificate as inactive
        const getUserCertificate =
          await uploadCertificateService.getSingleUploadCertificateByFilter({
            user: fileDetails.user,
            certificateType: fileDetails.certificateType,
            isActive: true,
            deletedAt: null,
          });
        if (getUserCertificate) {
          await uploadCertificateService.updateCertificateByFilter(
            { _id: getUserCertificate._id },
            {
              isActive: false,
            }
          );
        }

        fileDetails.status = 'approved';
        fileDetails.isActive = true;
      }
    }
  } catch (error) {
    console.error(error);
  }
  return fileDetails;
};

/**
 * Change file name if end date is changed
 *
 * @param {*} requestData
 * @param {*} existData
 * @returns
 */
exports.changeFileNameOnEndDate = async (requestData, existData) => {
  try {
    if ('endDate' in requestData && 'endDate' in existData) {
      const requestEndDate = new Date(requestData.endDate);
      const existEndDate = new Date(existData.endDate);

      if (requestEndDate !== existEndDate) {
        let searchDate = dateFormat(existEndDate, 'dd-mm-yyyy');
        let replaceDate = dateFormat(requestEndDate, 'dd-mm-yyyy');
        const newFileName = existData.fileName.replace(searchDate, replaceDate);
        return { ...requestData, fileName: newFileName };
      }
    }
  } catch (error) {
    console.error(error);
  }
  return requestData;
};

/**
 * Upload Multiple User Certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.uploadMultipleUserCertificate = async (req, res) => {
  try {
    let reqData = req.body;

    for (let data of reqData) {
      let { files, user } = data;
      Object.keys(files).forEach(async key => {
        files[key].user = user;
        files[key].account = req.userData.account;
        files[key].createdBy = user;
        files[key] = await this.replaceCertificateName(files[key], true);
        await uploadCertificateService.createUploadCertificate(files[key]);
      });
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Fix Inactive Certificates
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.fixInactiveCertificates = async (req, res) => {
  try {
    await uploadCertificateService.changeIsActiveStatus();

    const users = await userService.getUsersByFilter({ deletedAt: null });

    // eslint-disable-next-line no-undef
    const userIds = await Promise.all(users.map(user => user._id));

    const certificateType = await certificateTypeService.getCertificateType({
      deletedAt: null,
    });

    const certificateTypeIds = certificateType.map(certificate => certificate._id);
    const currentDate = new Date();

    for (const userId of userIds) {
      for (const certificateTypeId of certificateTypeIds) {
        const userCertificates = await uploadCertificateService.getCertificatesByUser({
          user: userId,
          certificateType: certificateTypeId,
          status: global.constant.APPROVED_STATUS,
        });

        if (userCertificates.length === 1) {
          const certificate = userCertificates[0];
          const endDate = new Date(certificate.endDate);

          await uploadCertificateService.updateDetail(certificate._id, {
            isActive: endDate > currentDate,
          });
        }

        if (userCertificates.length > 1) {
          const latestCertificate = userCertificates.reduce((latest, current) =>
            new Date(current.createdAt) > new Date(latest.createdAt) ? current : latest
          );

          const endDate = new Date(latestCertificate.endDate);

          await uploadCertificateService.updateDetail(latestCertificate._id, {
            isActive: endDate > currentDate,
          });
        }
      }
    }

    return res.status(200).json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_UPDATE));
  } catch (error) {
    return res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Certificate
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteUserCertificate = async (req, res) => {
  try {
    const id = req.params.id;
    const isExist = await uploadCertificateService.getSingleUploadCertificateByFilter({ _id: id });

    if (!isExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_CERTIFICATE));
    }
    const response = await uploadCertificateService.hardDeleteCertificate(id);

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CERTIFICATE_DELETED, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
