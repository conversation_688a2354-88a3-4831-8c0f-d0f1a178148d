const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app/server');
const User = require('../app/models/user.model');
const Account = require('../app/models/account.model');
const UploadCertificate = require('../app/models/upload-certificate.model');
const CertificateType = require('../app/models/certificate-type.model');
const Notification = require('../app/models/notification.model');
const NotificationRecipient = require('../app/models/notification-recipient.model');
const certificateNotificationService = require('../app/services/certificate-notification.service');
const uploadCertificateController = require('../app/controllers/upload-certificate.controller');

describe('Certificate Approval/Rejection Notifications', () => {
  let testAccount, testEngineer, testPM, testCertificateType, testCertificate;

  beforeAll(async () => {
    // Setup test data
    testAccount = await Account.create({
      name: 'Test Account',
      email: '<EMAIL>'
    });

    testEngineer = await User.create({
      firstName: 'Test',
      lastName: 'Engineer',
      email: '<EMAIL>',
      account: testAccount._id,
      isActive: true,
      isDeleted: false,
      fcmTokens: ['engineer-fcm-token-123']
    });

    testPM = await User.create({
      firstName: 'Test',
      lastName: 'PM',
      callingName: 'Project Manager',
      email: '<EMAIL>',
      account: testAccount._id,
      isActive: true,
      isDeleted: false,
      fcmTokens: ['pm-fcm-token-456']
    });

    testCertificateType = await CertificateType.create({
      name: 'Safety Training Certificate',
      account: testAccount._id
    });

    testCertificate = await UploadCertificate.create({
      user: testEngineer._id,
      account: testAccount._id,
      certificateType: testCertificateType._id,
      link: 'https://example.com/cert.pdf',
      fileName: 'safety-cert.pdf',
      name: 'Safety Training Certificate',
      status: 'pending',
      isActive: false
    });
  });

  afterAll(async () => {
    // Cleanup test data
    await User.deleteMany({});
    await Account.deleteMany({});
    await UploadCertificate.deleteMany({});
    await CertificateType.deleteMany({});
    await Notification.deleteMany({});
    await NotificationRecipient.deleteMany({});
  });

  describe('sendCertificateApprovalNotification', () => {
    it('should send approval notification to original uploader', async () => {
      const approvalData = {
        accountId: testAccount._id,
        approverId: testPM._id,
        originalUploaderId: testEngineer._id,
        certificateName: 'Safety Training Certificate',
        approverName: 'Project Manager PM'
      };

      // Mock the notification service to avoid actual FCM calls
      const mockSendSingleNotification = jest.spyOn(
        require('../app/services/notification.service'), 
        'sendSingleNotification'
      ).mockResolvedValue({
        success: true,
        notificationId: 'test-notification-id',
        recipientCount: 1
      });

      const result = await certificateNotificationService.sendCertificateApprovalNotification(approvalData);

      expect(result.success).toBe(true);
      expect(result.recipientCount).toBe(1);
      expect(mockSendSingleNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Certificate Approved',
          body: expect.stringContaining('Your certificate has been approved'),
          module: 'certificate_approval',
          priority: 'medium',
          actionUrl: '/certificates',
          accountId: testAccount._id,
          senderId: testPM._id,
          recipient: expect.objectContaining({
            _id: testEngineer._id
          })
        })
      );

      mockSendSingleNotification.mockRestore();
    });

    it('should handle case when original uploader is not found', async () => {
      const approvalData = {
        accountId: testAccount._id,
        approverId: testPM._id,
        originalUploaderId: new mongoose.Types.ObjectId(), // Non-existent user
        certificateName: 'Safety Training Certificate',
        approverName: 'Project Manager PM'
      };

      const result = await certificateNotificationService.sendCertificateApprovalNotification(approvalData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Original uploader not found or inactive');
      expect(result.recipientCount).toBe(0);
    });

    it('should handle case when original uploader is inactive', async () => {
      // Create inactive user
      const inactiveUser = await User.create({
        firstName: 'Inactive',
        lastName: 'User',
        email: '<EMAIL>',
        account: testAccount._id,
        isActive: false, // Inactive user
        isDeleted: false
      });

      const approvalData = {
        accountId: testAccount._id,
        approverId: testPM._id,
        originalUploaderId: inactiveUser._id,
        certificateName: 'Safety Training Certificate',
        approverName: 'Project Manager PM'
      };

      const result = await certificateNotificationService.sendCertificateApprovalNotification(approvalData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Original uploader not found or inactive');
      expect(result.recipientCount).toBe(0);

      await User.findByIdAndDelete(inactiveUser._id);
    });
  });

  describe('sendCertificateRejectionNotification', () => {
    it('should send rejection notification with reason to original uploader', async () => {
      const rejectionData = {
        accountId: testAccount._id,
        approverId: testPM._id,
        originalUploaderId: testEngineer._id,
        certificateName: 'Safety Training Certificate',
        approverName: 'Project Manager PM',
        rejectionReason: 'Certificate expired'
      };

      // Mock the notification service
      const mockSendSingleNotification = jest.spyOn(
        require('../app/services/notification.service'), 
        'sendSingleNotification'
      ).mockResolvedValue({
        success: true,
        notificationId: 'test-notification-id',
        recipientCount: 1
      });

      const result = await certificateNotificationService.sendCertificateRejectionNotification(rejectionData);

      expect(result.success).toBe(true);
      expect(result.recipientCount).toBe(1);
      expect(mockSendSingleNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Certificate Rejected',
          body: expect.stringContaining('Your certificate has been rejected'),
          body: expect.stringContaining('Certificate expired'),
          module: 'certificate_approval',
          priority: 'high',
          actionUrl: '/certificates',
          accountId: testAccount._id,
          senderId: testPM._id,
          recipient: expect.objectContaining({
            _id: testEngineer._id
          })
        })
      );

      mockSendSingleNotification.mockRestore();
    });

    it('should send rejection notification without reason when not provided', async () => {
      const rejectionData = {
        accountId: testAccount._id,
        approverId: testPM._id,
        originalUploaderId: testEngineer._id,
        certificateName: 'Safety Training Certificate',
        approverName: 'Project Manager PM'
        // No rejectionReason provided
      };

      const mockSendSingleNotification = jest.spyOn(
        require('../app/services/notification.service'), 
        'sendSingleNotification'
      ).mockResolvedValue({
        success: true,
        notificationId: 'test-notification-id',
        recipientCount: 1
      });

      const result = await certificateNotificationService.sendCertificateRejectionNotification(rejectionData);

      expect(result.success).toBe(true);
      expect(mockSendSingleNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          body: expect.not.stringContaining('Reason:')
        })
      );

      mockSendSingleNotification.mockRestore();
    });
  });

  describe('Controller Integration - sendApprovalRejectionNotification', () => {
    beforeEach(() => {
      // Mock external services
      jest.spyOn(require('../app/services/certificate-type.service'), 'getCertificateTypeById')
        .mockResolvedValue({ name: 'Safety Training Certificate' });
      
      jest.spyOn(require('../app/services/user.service'), 'getUserById')
        .mockResolvedValue([testEngineer]);
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should trigger approval notification when certificate is approved', async () => {
      const mockSendApprovalNotification = jest.spyOn(
        certificateNotificationService, 
        'sendCertificateApprovalNotification'
      ).mockResolvedValue({ success: true });

      const mockReq = {
        userData: {
          _id: testPM._id,
          account: testAccount._id,
          firstName: 'Test',
          lastName: 'PM',
          callingName: 'Project Manager'
        }
      };

      const mockResponse = {
        user: testEngineer._id,
        certificateType: testCertificateType._id
      };

      await uploadCertificateController.sendApprovalRejectionNotification(
        mockResponse, 
        'approved', 
        mockReq
      );

      expect(mockSendApprovalNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          accountId: testAccount._id,
          approverId: testPM._id,
          originalUploaderId: testEngineer._id,
          certificateName: 'Safety Training Certificate',
          approverName: 'Project Manager PM'
        })
      );

      mockSendApprovalNotification.mockRestore();
    });

    it('should trigger rejection notification when certificate is rejected', async () => {
      const mockSendRejectionNotification = jest.spyOn(
        certificateNotificationService, 
        'sendCertificateRejectionNotification'
      ).mockResolvedValue({ success: true });

      const mockReq = {
        userData: {
          _id: testPM._id,
          account: testAccount._id,
          firstName: 'Test',
          lastName: 'PM'
        }
      };

      const mockResponse = {
        user: testEngineer._id,
        certificateType: testCertificateType._id,
        reason: 'Document quality is poor'
      };

      await uploadCertificateController.sendApprovalRejectionNotification(
        mockResponse, 
        'rejected', 
        mockReq
      );

      expect(mockSendRejectionNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          accountId: testAccount._id,
          approverId: testPM._id,
          originalUploaderId: testEngineer._id,
          certificateName: 'Safety Training Certificate',
          approverName: 'Test PM',
          rejectionReason: 'Document quality is poor'
        })
      );

      mockSendRejectionNotification.mockRestore();
    });

    it('should handle errors gracefully without breaking main workflow', async () => {
      const mockSendApprovalNotification = jest.spyOn(
        certificateNotificationService, 
        'sendCertificateApprovalNotification'
      ).mockRejectedValue(new Error('Notification service error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const mockReq = {
        userData: {
          _id: testPM._id,
          account: testAccount._id,
          firstName: 'Test',
          lastName: 'PM'
        }
      };

      const mockResponse = {
        user: testEngineer._id,
        certificateType: testCertificateType._id
      };

      // Should not throw error
      await expect(
        uploadCertificateController.sendApprovalRejectionNotification(
          mockResponse, 
          'approved', 
          mockReq
        )
      ).resolves.not.toThrow();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error in certificate approval/rejection notification process')
      );

      mockSendApprovalNotification.mockRestore();
      consoleSpy.mockRestore();
    });
  });
});
