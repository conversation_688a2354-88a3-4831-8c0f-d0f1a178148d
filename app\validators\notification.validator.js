const { body, param, query, constantUtils } = require('./parent.validator');

exports.updateReadStatusValidationRule = () => {
  return [
    param('notificationRecipientId').isMongoId().withMessage('Invalid notification recipient ID'),
    body('isRead')
      .isBoolean()
      .withMessage('isRead must be a boolean value')
      .notEmpty()
      .withMessage('isRead field is required'),
  ];
};

exports.toggleReadStatusValidationRule = () => {
  return [
    param('notificationRecipientId').isMongoId().withMessage('Invalid notification recipient ID'),
  ];
};
