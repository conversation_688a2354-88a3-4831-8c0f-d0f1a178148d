const Permission = require('../models/permission.model');
const AccountLicence = require('../models/account-licence.model');
const RoleAgreement = require('../models/role-agreement.model');
const User = require('../models/user.model');
const Notification = require('../models/notification.model');
const NotificationRecipient = require('../models/notification-recipient.model');
const notificationService = require('./notification.service');
const { logger } = require('../utils/logger.utils');
const constantUtils = require('../utils/constants.utils');

/**
 * Find users with certificate approval permissions using the database query sequence:
 * 1. Query permissions table to find permission by name (certificate approval permission)
 * 2. Query accountLicenses table using accountId and permissionId from step 1
 * 3. Query roleAgreements table with accountLicenseId from step 2, where deleteAt: null, isActive: true, and agreement.read: true
 * 4. Query users table to find all users whose roleId matches the role IDs from step 3
 *
 * @param {string} accountId - The account ID to search within
 * @returns {Promise<Array>} Array of users with certificate approval permissions
 */
exports.getUsersWithCertificateApprovalPermission = async accountId => {
  try {
    // Step 1: Find certificate approval permission
    const certificateApprovalPermission = await Permission.findOne({
      name: 'Certificate Approval',
    });

    if (!certificateApprovalPermission) {
      logger.warn(constantUtils.CERTIFICATE_APPROVAL_PERMISSION_NOT_FOUND, { accountId });
      return [];
    }

    // Step 2: Find account licenses for this account and permission
    const accountLicenses = await AccountLicence.find({
      account: accountId,
      permission: certificateApprovalPermission._id,
      isApproved: true, // Only approved licenses
    });

    if (accountLicenses.length === 0) {
      logger.info('No approved account licenses found for certificate approval permission', {
        accountId,
        permissionId: certificateApprovalPermission._id,
      });
      return [];
    }

    const accountLicenseIds = accountLicenses.map(license => license._id);

    // Step 3: Find role agreements with read permission
    const roleAgreements = await RoleAgreement.find({
      account: accountId,
      accountLicence: { $in: accountLicenseIds },
      deletedAt: null,
      isActive: true,
      'agreement.read': true,
    }).populate('role');

    if (roleAgreements.length === 0) {
      logger.info('No active role agreements found with read permission', {
        accountId,
        accountLicenseIds,
      });
      return [];
    }

    const roleIds = roleAgreements.map(agreement => agreement.role._id);

    // Step 4: Find users with matching roles
    const users = await User.find({
      account: accountId,
      role: { $in: roleIds },
      isActive: true,
      isDeleted: false,
    }).select('_id firstName lastName callingName email fcmTokens role');

    logger.info('Found users with certificate approval permission', {
      accountId,
      userCount: users.length,
      roleIds: roleIds.map(id => id.toString()),
    });

    return users;
  } catch (error) {
    logger.error('Error finding users with certificate approval permission', {
      error: error.message,
      accountId,
    });
    throw error;
  }
};

/**
 * Find engineers (mobile users) for web-to-mobile notifications
 * Engineers are users with mobile access type roles
 *
 * @param {string} accountId - The account ID to search within
 * @returns {Promise<Array>} Array of engineer users
 */
exports.getEngineersForNotification = async accountId => {
  try {
    const engineers = await User.find({
      account: accountId,
      isActive: true,
      isDeleted: false,
    })
      .populate({
        path: 'role',
        match: {
          accessType: { $in: ['mobile', 'both'] },
          isActive: true,
          deletedAt: null,
        },
      })
      .select('_id firstName lastName callingName email fcmTokens role');

    // Filter out users where role population failed (no matching role)
    const validEngineers = engineers.filter(user => user.role);

    return validEngineers;
  } catch (error) {
    logger.error('Error finding engineers for notification');
    throw error;
  }
};

/**
 * Create and send certificate notifications
 *
 * @param {Object} notificationData - Notification data
 * @param {Array} recipients - Array of recipient user objects
 * @returns {Promise<Object>} Result object with success status and details
 */
exports.createAndSendCertificateNotification = async (notificationData, recipients) => {
  try {
    if (!recipients || recipients.length === 0) {
      logger.warn(constantUtils.NO_USERS_WITH_CERTIFICATE_APPROVAL_PERMISSION, {
        accountId: notificationData.accountId,
      });
      return {
        success: true,
        message: 'No recipients found for notification',
        notificationId: null,
        recipientCount: 0,
        pushNotificationResults: [],
      };
    }

    // Create notification record
    const notification = await Notification.create({
      title: notificationData.title,
      body: notificationData.body,
      module: notificationData.module || 'certificate_approval',
      priority: notificationData.priority || 'medium',
      actionUrl: notificationData.actionUrl || null,
      account: notificationData.accountId,
      sender: notificationData.senderId,
      createdBy: notificationData.senderId,
    });

    // Create notification recipients
    const notificationRecipients = recipients.map(recipient => ({
      notification: notification._id,
      recipient: recipient._id,
    }));

    await NotificationRecipient.insertMany(notificationRecipients);

    // Send push notifications
    const pushNotificationResults = [];

    for (const recipient of recipients) {
      if (recipient.fcmTokens && recipient.fcmTokens.length > 0) {
        for (const fcmToken of recipient.fcmTokens) {
          try {
            const pushResult = await notificationService.sendNotification(fcmToken, {
              title: notificationData.title,
              body: notificationData.body,
            });

            pushNotificationResults.push({
              recipientId: recipient._id,
              fcmToken: fcmToken,
              success: true,
              result: pushResult,
            });
          } catch (pushError) {
            logger.error('Failed to send push notification', {
              error: pushError.message,
              recipientId: recipient._id,
              fcmToken: fcmToken,
            });

            pushNotificationResults.push({
              recipientId: recipient._id,
              fcmToken: fcmToken,
              success: false,
              error: pushError.message,
            });
          }
        }
      } else {
        logger.warn('Recipient has no FCM tokens', { recipientId: recipient._id });
        pushNotificationResults.push({
          recipientId: recipient._id,
          fcmToken: null,
          success: false,
          error: 'No FCM tokens available',
        });
      }
    }

    const successfulPushes = pushNotificationResults.filter(result => result.success).length;

    logger.info(constantUtils.CERTIFICATE_NOTIFICATION_SENT, {
      notificationId: notification._id,
      recipientCount: recipients.length,
      successfulPushes: successfulPushes,
      totalPushAttempts: pushNotificationResults.length,
    });

    return {
      success: true,
      message: constantUtils.CERTIFICATE_NOTIFICATION_SENT,
      notificationId: notification._id,
      recipientCount: recipients.length,
      pushNotificationResults: pushNotificationResults,
    };
  } catch (error) {
    logger.error(constantUtils.CERTIFICATE_NOTIFICATION_FAILED, {
      error: error.message,
      accountId: notificationData.accountId,
    });
    throw error;
  }
};

/**
 * Send web-to-mobile certificate notification
 * When Admin uploads certificate from Web without "Internal" checkbox, notify Engineers on Mobile
 *
 * @param {Object} certificateData - Certificate data
 * @returns {Promise<Object>} Result object
 */
exports.sendWebToMobileNotification = async certificateData => {
  try {
    const engineers = await this.getEngineersForNotification(certificateData.accountId);

    const notificationData = {
      title: constantUtils.CERTIFICATE_UPLOADED_WEB_TO_MOBILE_TITLE,
      body: `${constantUtils.CERTIFICATE_UPLOADED_WEB_TO_MOBILE_BODY}${
        certificateData.certificateName ? ` - ${certificateData.certificateName}` : ''
      }${certificateData.firstName ? ` for ${certificateData.firstName}` : ''}`,
      module: 'certificate_approval',
      accountId: certificateData.accountId,
      senderId: certificateData.senderId,
    };

    return await this.createAndSendCertificateNotification(notificationData, engineers);
  } catch (error) {
    logger.error('Error sending web-to-mobile certificate notification', {
      error: error.message,
      certificateData,
    });
    throw error;
  }
};

/**
 * Send mobile-to-web certificate notification
 * When any user uploads certificate from Mobile, notify users with certificate approval permissions
 *
 * @param {Object} certificateData - Certificate data
 * @returns {Promise<Object>} Result object
 */
exports.sendMobileToWebNotification = async certificateData => {
  try {
    const approvers = await this.getUsersWithCertificateApprovalPermission(
      certificateData.accountId
    );

    const notificationData = {
      title: constantUtils.CERTIFICATE_UPLOADED_MOBILE_TO_WEB_TITLE,
      body: `${constantUtils.CERTIFICATE_UPLOADED_MOBILE_TO_WEB_BODY}${
        certificateData.certificateName ? ` - ${certificateData.certificateName}` : ''
      }${certificateData.userName ? ` by ${certificateData.userName}` : ''}`,
      module: 'certificate_approval',
      priority: 'high', // Higher priority for approval requests
      actionUrl: '/certificate-approval', // Deep link to certificate approval section
      accountId: certificateData.accountId,
      senderId: certificateData.senderId,
    };

    return await this.createAndSendCertificateNotification(notificationData, approvers);
  } catch (error) {
    logger.error('Error sending mobile-to-web certificate notification', {
      error: error.message,
      certificateData,
    });
    throw error;
  }
};
